<?php
// Start session to track user progress
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit;
}

// Database connection
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'visual_novel_db';

// Connect to database
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get unlocked chapters for this user
$unlocked_chapters = [];
$sql = "SELECT chapter_id FROM user_progress WHERE user_id = ? AND completed = TRUE";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $unlocked_chapters[] = $row['chapter_id'];
}

// Define all chapters
$chapters = [
    // Chapter 1 episodes
    '1' => [
        ['id' => '01-01', 'title' => 'First Meeting', 'thumbnail' => '../assets/episode1-thumbnail.png', 'url' => '../episode1/game-interface.html'],
        ['id' => '01-02', 'title' => 'Getting to Know You', 'thumbnail' => '../assets/episode2-thumbnail.webp', 'url' => '../episode1/game-interface2.html'],
        ['id' => '01-03', 'title' => 'The Park Date', 'thumbnail' => '../assets/episode3-thumbnail.webp', 'url' => '../episode1/game-interface3.html'],
        ['id' => '01-04', 'title' => 'Coffee Shop Talk', 'thumbnail' => '../assets/episode4-thumbnail.png', 'url' => '../episode1/game-interface4.html'],
        ['id' => '01-05', 'title' => 'Evening Walk', 'thumbnail' => '../assets/episode5-thumbnail.png', 'url' => '../episode1/game-interface5.html'],
        ['id' => '01-06', 'title' => 'First Kiss', 'thumbnail' => '../assets/episode6-thumbnail.png', 'url' => '../episode1/game-interface6.html']
    ]
];

// Function to check if chapter is unlocked
function isChapterUnlocked($chapter_id, $unlocked_chapters) {
    // First chapter is always unlocked
    if ($chapter_id === '01-01') {
        return true;
    }
    
    // Check if chapter is in unlocked list
    return in_array($chapter_id, $unlocked_chapters);
}

// Get current chapter from query parameter or default to 1
$currentChapter = isset($_GET['chapter']) ? intval($_GET['chapter']) : 1;
if ($currentChapter < 1 || $currentChapter > 2) {
    $currentChapter = 1;
}

// Total number of chapters
$totalChapters = 2;
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Story Chapters</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" href="../MenuButton/shared-styles.css">
  <style>
    /* CSS Variables for Harem Novel Theme */
    :root {
      --primary-color: #4a90e2;
      --secondary-color: #7b68ee;
      --tertiary-color: #87ceeb;
      --accent-color: #ff69b4;
      --dark-bg: linear-gradient(135deg, #0f1419 0%, #1a2332 25%, #2d3748 50%, #1a2332 75%, #0f1419 100%);
      --card-bg: linear-gradient(135deg, rgba(74, 144, 226, 0.15), rgba(123, 104, 238, 0.15));
      --text-light: #ffffff;
      --text-gray: #b0c4de;
      --blue-gradient: linear-gradient(45deg, #4a90e2, #7b68ee, #87ceeb);
      --harem-pink: #ff69b4;
      --deep-blue: #191970;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Roboto', sans-serif;
      background: var(--dark-bg);
      color: var(--text-light);
      overflow: hidden;
      height: 100vh;
      position: relative;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(74, 144, 226, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(123, 104, 238, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(135, 206, 235, 0.2) 0%, transparent 50%);
      z-index: -1;
      animation: backgroundShift 20s ease-in-out infinite;
    }

    @keyframes backgroundShift {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    #story-container {
      position: relative;
      width: 100%;
      height: 100%;
      background:
        linear-gradient(135deg,
          rgba(15, 20, 25, 0.9) 0%,
          rgba(26, 35, 50, 0.8) 25%,
          rgba(45, 55, 72, 0.7) 50%,
          rgba(26, 35, 50, 0.8) 75%,
          rgba(15, 20, 25, 0.9) 100%
        );
      backdrop-filter: blur(2px);
      overflow: hidden;
    }

    /* Top navigation */
    .top-nav {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 25px;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.3) 0%,
        rgba(123, 104, 238, 0.3) 50%,
        rgba(135, 206, 235, 0.3) 100%
      );
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(74, 144, 226, 0.3);
      z-index: 100;
      box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
    }

    /* Navigation styling */
    .nav-left, .nav-right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .page-title {
      font-size: 20px;
      font-weight: 700;
      color: var(--text-light);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .page-title i {
      color: var(--accent-color);
      font-size: 18px;
    }

    .user-info {
      background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(255,105,180,0.3));
      padding: 10px 20px;
      border-radius: 15px;
      border: 1px solid rgba(255,255,255,0.15);
      box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .user-info span {
      font-weight: 600;
      letter-spacing: 0.5px;
      color: var(--text-light);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .top-nav {
        padding: 12px 15px;
        flex-wrap: wrap;
        gap: 10px;
      }

      .nav-left, .nav-right {
        gap: 15px;
      }

      .page-title {
        font-size: 16px;
      }
    }

    /* Chapter selection */
    .chapter-selection {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 0;
      transition: opacity 0.5s ease;
    }

    .chapter-track {
      display: flex;
      padding: 30px 0;
      overflow-x: auto;
      width: 90%;
      justify-content: flex-start;
      padding-left: 20px;
      -ms-overflow-style: none;
      scrollbar-width: none;
      scroll-behavior: smooth;
      gap: 20px;
    }

    .chapter-track::-webkit-scrollbar {
      display: none;
    }

    .chapter-card {
      width: 220px;
      height: 300px;
      margin: 0 10px;
      background: var(--card-bg);
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      border-radius: 15px;
      border: 2px solid rgba(74, 144, 226, 0.3);
      box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      cursor: pointer;
      backdrop-filter: blur(10px);
    }

    .chapter-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, transparent 60%, rgba(0,0,0,0.8));
      z-index: 2;
      pointer-events: none;
    }

    .chapter-card:hover {
      transform: translateY(-10px) scale(1.05);
      border-color: var(--accent-color);
      box-shadow: 0 15px 35px rgba(255, 105, 180, 0.4);
    }

    .chapter-card img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .chapter-card:hover img {
      transform: scale(1.1);
    }

    .chapter-number {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.9) 0%,
        rgba(123, 104, 238, 0.9) 100%
      );
      color: white;
      padding: 15px;
      font-size: 16px;
      font-weight: 700;
      text-align: center;
      letter-spacing: 1px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
      z-index: 3;
    }

    .chapter-locked {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(0,0,0,0.8) 0%,
        rgba(45, 55, 72, 0.8) 100%
      );
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 15px;
      backdrop-filter: blur(5px);
      z-index: 4;
    }

    @keyframes lockPulse {
      0% { transform: scale(1); opacity: 0.8; }
      50% { transform: scale(1.1); opacity: 1; }
      100% { transform: scale(1); opacity: 0.8; }
    }

    .lock-icon {
      width: 60px;
      height: 60px;
      color: var(--accent-color);
      font-size: 60px;
      animation: lockPulse 2s infinite;
      text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
    }

    .lock-icon::before {
      content: '🔒';
    }

    /* Navigation arrows for chapter track */
    .chapter-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.8) 0%,
        rgba(123, 104, 238, 0.8) 100%
      );
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 22px;
      font-weight: bold;
      cursor: pointer;
      z-index: 20;
      box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      backdrop-filter: blur(10px);
    }

    .chapter-nav-left {
      left: 20px;
    }

    .chapter-nav-left:hover {
      background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.8) 0%,
        rgba(74, 144, 226, 0.8) 100%
      );
      box-shadow: 0 8px 20px rgba(255, 105, 180, 0.5);
      transform: translateY(-50%) scale(1.1);
    }

    .chapter-nav-right {
      right: 20px;
    }

    .chapter-nav-right:hover {
      background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.8) 0%,
        rgba(74, 144, 226, 0.8) 100%
      );
      box-shadow: 0 8px 20px rgba(255, 105, 180, 0.5);
      transform: translateY(-50%) scale(1.1);
    }

    /* Navigation buttons */
    .chapter-navigation {
      position: absolute;
      bottom: 30px;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }

    .chapter-controls {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .nav-button {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.8) 0%,
        rgba(123, 104, 238, 0.8) 100%
      );
      border: 2px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .nav-button:hover {
      background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.8) 0%,
        rgba(74, 144, 226, 0.8) 100%
      );
      box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
      transform: translateY(-3px) scale(1.05);
    }

    .nav-icon {
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    .next-chapter {
      background: linear-gradient(135deg,
        rgba(0,0,0,0.8) 0%,
        rgba(74, 144, 226, 0.3) 100%
      );
      color: white;
      padding: 10px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
      pointer-events: none;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .next-button:hover + .next-chapter,
    .next-button:hover ~ .next-chapter {
      opacity: 1;
      transform: translateY(0);
    }

    .chapter-button {
      background: linear-gradient(135deg,
        rgba(74, 144, 226, 0.9) 0%,
        rgba(123, 104, 238, 0.9) 100%
      );
      color: #fff;
      padding: 15px 40px;
      border-radius: 25px;
      font-size: 18px;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
      min-width: 200px;
      text-transform: uppercase;
      letter-spacing: 2px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(255,255,255,0.2);
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      backdrop-filter: blur(10px);
    }

    .chapter-button:hover {
      background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.9) 0%,
        rgba(74, 144, 226, 0.9) 100%
      );
      box-shadow: 0 12px 35px rgba(255, 105, 180, 0.5);
      transform: translateY(-3px);
    }

    .chapter-number-display {
      position: relative;
      z-index: 2;
      display: inline-block;
      padding: 0 5px;
    }
    /* Responsive Design */
    @media (max-width: 768px) {
      .chapter-card {
        width: 180px;
        height: 250px;
        margin: 0 8px;
      }

      .chapter-track {
        padding-left: 15px;
        gap: 15px;
      }

      .chapter-nav {
        width: 45px;
        height: 45px;
        font-size: 18px;
      }

      .nav-button {
        width: 50px;
        height: 50px;
      }

      .nav-icon {
        font-size: 20px;
      }

      .chapter-button {
        padding: 12px 30px;
        font-size: 16px;
        min-width: 160px;
      }

      .chapter-navigation {
        bottom: 20px;
      }

      .chapter-controls {
        gap: 15px;
      }
    }

    @media (max-width: 480px) {
      .chapter-card {
        width: 160px;
        height: 220px;
      }

      .chapter-track {
        width: 95%;
        padding-left: 10px;
      }

      .chapter-nav-left {
        left: 10px;
      }

      .chapter-nav-right {
        right: 10px;
      }
    }

  </style>
</head>
<body>
  <div id="story-container">
    
    <!-- Top navigation -->
    <div class="top-nav">
      <!-- Left side with back button and title -->
      <div class="nav-left">
        <a href="../menu/home.php" class="back-btn style-large">
          <i class="fas fa-arrow-left"></i>
        </a>
        <div class="page-title">
          <i class="fas fa-book-open"></i> Story Chapters
        </div>
      </div>

      <!-- Right side with user info -->
      <div class="nav-right">
        <div class="user-info">
          <span>Welcome, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Player'); ?></span>
        </div>
      </div>
    </div>
    
    <!-- Chapter selection -->
    <div id="chapter1-selection" class="chapter-selection" style="<?php echo $currentChapter === 2 ? 'display: none;' : ''; ?>">
      <div class="chapter-nav chapter-nav-left" onclick="scrollChapters('left')">←</div>
      <div class="chapter-track">
        <?php foreach ($chapters['1'] as $chapter): ?>
          <?php $isUnlocked = isChapterUnlocked($chapter['id'], $unlocked_chapters); ?>
          <div class="chapter-card <?php echo ($chapter['id'] === '01-01') ? 'chapter-active' : ''; ?>" 
               data-chapter-id="<?php echo $chapter['id']; ?>"
               data-chapter-url="<?php echo $chapter['url']; ?>"
               data-is-unlocked="<?php echo $isUnlocked ? 'true' : 'false'; ?>">
            <img src="<?php echo $chapter['thumbnail']; ?>" alt="<?php echo $chapter['id']; ?>">
            <div class="chapter-number"><?php echo $chapter['id']; ?></div>
            <?php if (!$isUnlocked): ?>
              <div class="chapter-locked">
                <div class="lock-icon"></div>
              </div>
            <?php else: ?>
              <div class="chapter-unlocked"></div>
            <?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="chapter-nav chapter-nav-right" onclick="scrollChapters('right')">→</div>
    </div>
    
    <!-- Navigation buttons -->
    <div class="chapter-navigation">
      <div class="chapter-controls">
        <div class="nav-button prev-button" style="visibility: <?php echo $currentChapter <= 1 ? 'hidden' : 'visible'; ?>">
          <div class="nav-icon">←</div>
        </div>
        <button class="chapter-button">
          <span class="chapter-number-display">CHAPTER <?php echo $currentChapter; ?></span>
        </button>
        <div class="nav-button next-button" onclick="window.location.href='story-chapters2.php'" style="visibility: <?php echo $currentChapter >= $totalChapters ? 'hidden' : 'visible'; ?>">
          <div class="nav-icon">→</div>
        </div>
      </div>
      <div class="next-chapter">Next Chapter</div>
    </div>
  </div>

  <script>
    // Current chapter
    let currentChapter = <?php echo $currentChapter; ?>;
    const totalChapters = <?php echo $totalChapters; ?>;
    
    // Function to navigate between chapters
    function navigateChapter(direction) {
      if (direction === 'prev') {
        if (currentChapter > 1) {
          // Transition to previous chapter
          fadeOutCurrentChapter();
          setTimeout(() => {
            window.location.href = 'story-chapters.php?chapter=' + (currentChapter - 1);
          }, 500);
        }
      } else {
        if (currentChapter < totalChapters) {
          // Transition to next chapter
          fadeOutCurrentChapter();
          setTimeout(() => {
            window.location.href = 'story-chapters.php?chapter=' + (currentChapter + 1);
          }, 500);
        }
      }
    }
    
    // Function to fade out current chapter before transition
    function fadeOutCurrentChapter() {
      const container = document.getElementById('story-container');
      const currentSelection = document.getElementById('chapter' + currentChapter + '-selection');
      
      // Fade out
      container.style.opacity = '0.5';
      currentSelection.style.opacity = '0';
      currentSelection.style.transform = 'translateY(-20px)';
    }
    
    // Update UI to reflect current chapter
    function updateChapterUI() {
      // Update chapter display
      document.querySelector('.chapter-number-display').textContent = `Chapter ${currentChapter}`;
      
      // Update button states
      const prevButton = document.querySelector('.prev-button');
      const nextButton = document.querySelector('.next-button');
      
      if (currentChapter === 1) {
        prevButton.style.opacity = '0.5';
        prevButton.style.cursor = 'not-allowed';
      } else {
        prevButton.style.opacity = '1';
        prevButton.style.cursor = 'pointer';
      }
      
      if (currentChapter === totalChapters) {
        nextButton.style.opacity = '0.5';
        nextButton.style.cursor = 'not-allowed';
      } else {
        nextButton.style.opacity = '1';
        nextButton.style.cursor = 'pointer';
      }
    }
    
    // Function to scroll chapters left or right
    function scrollChapters(direction, chapterNum = currentChapter) {
      const track = document.querySelector(`#chapter${chapterNum}-selection .chapter-track`);
      const cardWidth = 230; // Card width + margin
      
      if (direction === 'left') {
        track.scrollBy({ left: -cardWidth * 2, behavior: 'smooth' });
      } else {
        track.scrollBy({ left: cardWidth * 2, behavior: 'smooth' });
      }
    }
    
    // Initialize the chapter UI
    document.addEventListener('DOMContentLoaded', function() {
      updateChapterUI();
      
      // Set up hover effect for next chapter text
      const nextButton = document.querySelector('.next-button');
      const nextChapterText = document.querySelector('.next-chapter');
      
      nextButton.addEventListener('mouseenter', function() {
        nextChapterText.style.opacity = '1';
        nextChapterText.style.transform = 'translateY(0)';
      });
      
      nextButton.addEventListener('mouseleave', function() {
        nextChapterText.style.opacity = '0';
        nextChapterText.style.transform = 'translateY(10px)';
      });
      
      // Add click event to chapter cards
      document.querySelectorAll('.chapter-card').forEach(card => {
        card.addEventListener('click', function() {
          const chapterId = this.dataset.chapterId;
          const chapterUrl = this.dataset.chapterUrl;
          const isUnlocked = this.dataset.isUnlocked === 'true';
          
          // Set as active
          const chapterNum = chapterId.substring(0, 2);
          document.querySelectorAll(`#chapter${chapterNum[0]}-selection .chapter-card`).forEach(c => {
            c.classList.remove('chapter-active');
          });
          this.classList.add('chapter-active');
          
          // If unlocked, navigate to chapter
          if (isUnlocked) {
            window.location.href = chapterUrl;
          } else {
            alert(`Chapter ${chapterId} is locked. Complete previous chapters to unlock it.`);
          }
        });
      });
      
      // Apply fade-in animation to current chapter selection
      const currentSelection = document.getElementById('chapter' + currentChapter + '-selection');
      if (currentSelection) {
        currentSelection.classList.add('fade-in');
      }
    });
  </script>
</body>
</html>































