/**
 * Character Detail Page JavaScript
 * Handles tab navigation and character profile interactions for harem collection
 */

// ===== TAB NAVIGATION =====

/**
 * Show specific tab and hide others
 * @param {string} tabName - Name of the tab to show
 */
function showTab(tabName) {
    // Hide all tab panels
    const tabPanels = document.querySelectorAll('.tab-panel');
    tabPanels.forEach(panel => {
        panel.classList.remove('active');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab panel
    const selectedPanel = document.getElementById(`${tabName}-tab`);
    if (selectedPanel) {
        selectedPanel.classList.add('active');
    }

    // Add active class to clicked button
    const clickedButton = event.target;
    if (clickedButton) {
        clickedButton.classList.add('active');
    }

    // Add animation effect
    animateTabTransition(selectedPanel);
}

/**
 * Animate tab transition
 * @param {HTMLElement} panel - Tab panel element
 */
function animateTabTransition(panel) {
    if (!panel) return;
    
    panel.style.opacity = '0';
    panel.style.transform = 'translateY(10px)';
    
    setTimeout(() => {
        panel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        panel.style.opacity = '1';
        panel.style.transform = 'translateY(0)';
    }, 50);
}

// ===== CHARACTER ACTIONS =====

/**
 * Handle character favoriting
 * @param {number} characterId - ID of the character
 */
function toggleFavorite(characterId) {
    // This would connect to a backend to save favorite status
    const heartIcon = document.querySelector('.favorite-btn i');
    const isFavorited = heartIcon.classList.contains('fas');

    if (isFavorited) {
        heartIcon.classList.remove('fas');
        heartIcon.classList.add('far');
        showNotification('Removed from favorites', 'info');
    } else {
        heartIcon.classList.remove('far');
        heartIcon.classList.add('fas');
        showNotification('Added to favorites! 💖', 'success');
    }
}

/**
 * Handle gallery image viewing
 * @param {string} imageSrc - Source of the image to view
 * @param {string} caption - Caption for the image
 */
function viewGalleryImage(imageSrc, caption) {
    // Create modal for full-size image viewing
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="modal-backdrop" onclick="closeImageModal()"></div>
        <div class="modal-content">
            <button class="modal-close" onclick="closeImageModal()">×</button>
            <img src="${imageSrc}" alt="${caption}">
            <div class="modal-caption">${caption}</div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

/**
 * Close image modal
 */
function closeImageModal() {
    const modal = document.querySelector('.image-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// ===== INTERACTIVE EFFECTS =====

/**
 * Add hover effects to profile items
 */
function initializeProfileEffects() {
    const profileItems = document.querySelectorAll('.profile-item');

    profileItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255, 105, 180, 0.1)';
            this.style.borderColor = 'rgba(255, 105, 180, 0.3)';
            this.style.transform = 'translateX(5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.05)';
            this.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * Add click effects to gallery items
 */
function initializeGalleryEffects() {
    const galleryItems = document.querySelectorAll('.gallery-item:not(.placeholder)');

    galleryItems.forEach(item => {
        const img = item.querySelector('img');
        const caption = item.querySelector('.gallery-caption');

        if (img && caption) {
            item.addEventListener('click', function() {
                viewGalleryImage(img.src, caption.textContent);
            });

            item.style.cursor = 'pointer';

            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 8px 25px rgba(255, 105, 180, 0.3)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = 'none';
            });
        }
    });
}

/**
 * Add effects to personality tags
 */
function initializePersonalityEffects() {
    const personalityTags = document.querySelectorAll('.personality-tag');

    personalityTags.forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255, 105, 180, 0.2)';
            this.style.transform = 'scale(1.1)';
        });

        tag.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 105, 180, 0.1)';
            this.style.transform = 'scale(1)';
        });
    });
}

// ===== KEYBOARD NAVIGATION =====

/**
 * Handle keyboard navigation for tabs
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const activeTab = document.querySelector('.tab-btn.active');
        
        if (!activeTab) return;
        
        let currentIndex = Array.from(tabButtons).indexOf(activeTab);
        let newIndex = currentIndex;
        
        switch(e.key) {
            case 'ArrowLeft':
                newIndex = currentIndex > 0 ? currentIndex - 1 : tabButtons.length - 1;
                break;
            case 'ArrowRight':
                newIndex = currentIndex < tabButtons.length - 1 ? currentIndex + 1 : 0;
                break;
            default:
                return;
        }
        
        e.preventDefault();
        tabButtons[newIndex].click();
        tabButtons[newIndex].focus();
    });
}

// ===== PARTICLE EFFECTS =====

/**
 * Create floating particles for element theme
 */
function createElementParticles() {
    const container = document.querySelector('.character-detail-container');
    if (!container) return;
    
    // Get element from character data (if available)
    const elementBadge = document.querySelector('.element-badge');
    let elementType = 'default';
    
    if (elementBadge) {
        const elementText = elementBadge.textContent.toLowerCase();
        if (elementText.includes('fire')) elementType = 'fire';
        else if (elementText.includes('ice')) elementType = 'ice';
        else if (elementText.includes('lightning')) elementType = 'lightning';
    }
    
    // Create particles based on element
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            createParticle(container, elementType);
        }, i * 200);
    }
    
    // Continue creating particles periodically
    setInterval(() => {
        createParticle(container, elementType);
    }, 3000);
}

/**
 * Create individual particle
 * @param {HTMLElement} container - Container element
 * @param {string} elementType - Element type for particle styling
 */
function createParticle(container, elementType) {
    const particle = document.createElement('div');
    particle.className = 'element-particle';
    
    // Set particle appearance based on element
    let particleChar = '✨';
    let particleColor = '#ffffff';
    
    switch(elementType) {
        case 'fire':
            particleChar = Math.random() > 0.5 ? '🔥' : '✨';
            particleColor = '#ff5722';
            break;
        case 'ice':
            particleChar = Math.random() > 0.5 ? '❄️' : '✨';
            particleColor = '#00bcd4';
            break;
        case 'lightning':
            particleChar = Math.random() > 0.5 ? '⚡' : '✨';
            particleColor = '#ffeb3b';
            break;
    }
    
    particle.textContent = particleChar;
    particle.style.cssText = `
        position: fixed;
        font-size: ${Math.random() * 10 + 10}px;
        color: ${particleColor};
        pointer-events: none;
        z-index: 1000;
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 20}px;
        opacity: 0.7;
        animation: floatUp ${Math.random() * 3 + 4}s linear forwards;
    `;
    
    container.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 7000);
}

// Add CSS for particle animation
const style = document.createElement('style');
style.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 0.7;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translateY(-${window.innerHeight + 100}px) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// ===== INITIALIZATION =====

/**
 * Initialize all character detail page functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize interactive effects
    initializeProfileEffects();
    initializeGalleryEffects();
    initializePersonalityEffects();

    // Initialize keyboard navigation
    initializeKeyboardNavigation();

    // Create element particles
    createElementParticles();

    // Add focus effects to action buttons
    const actionButtons = document.querySelectorAll('.action-btn:not(.disabled)');
    actionButtons.forEach(button => {
        button.addEventListener('focus', function() {
            this.style.outline = '2px solid #ff69b4';
            this.style.outlineOffset = '2px';
        });

        button.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });

    // Add smooth scrolling for tab content
    const tabContent = document.querySelector('.tab-content');
    if (tabContent) {
        tabContent.style.scrollBehavior = 'smooth';
    }

    // Add image modal styles
    const modalStyles = document.createElement('style');
    modalStyles.textContent = `
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            cursor: pointer;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            background: rgba(20, 25, 40, 0.95);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #ff69b4;
            font-size: 24px;
            cursor: pointer;
            z-index: 1;
        }

        .modal-content img {
            max-width: 100%;
            max-height: 70vh;
            border-radius: 8px;
        }

        .modal-caption {
            text-align: center;
            color: #ff69b4;
            margin-top: 10px;
            font-weight: bold;
        }
    `;
    document.head.appendChild(modalStyles);

    console.log('Character harem collection page initialized successfully');
});

// ===== EXPORT FOR MODULE USAGE =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showTab,
        toggleFavorite,
        viewGalleryImage,
        closeImageModal
    };
}

