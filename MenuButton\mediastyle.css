/* Media Queries for Responsive Design - Harem Collection */

/* Large Desktop */
@media (min-width: 1200px) {
    .character-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
    }
    
    .character-card {
        padding: 15px;
    }
    
    .character-name {
        font-size: 14px;
    }
    
    .character-title {
        font-size: 11px;
    }
}

/* Desktop */
@media (max-width: 1199px) and (min-width: 992px) {
    .character-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 18px;
    }
    
    .character-card {
        padding: 12px;
    }
}

/* Tablet Landscape */
@media (max-width: 991px) and (min-width: 768px) {
    .character-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .character-card {
        padding: 10px;
    }
    
    .character-name {
        font-size: 11px;
    }
    
    .character-title {
        font-size: 9px;
    }
}

/* Tablet Portrait */
@media (max-width: 767px) and (min-width: 576px) {
    .character-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }
    
    .sidebar {
        display: none;
    }
    
    .character-content {
        padding: 15px;
    }
    
    .character-card {
        padding: 8px;
    }
    
    .character-name {
        font-size: 10px;
    }
    
    .character-title {
        font-size: 8px;
    }
    
    .star {
        font-size: 9px;
    }
}

/* Mobile Landscape */
@media (max-width: 575px) and (min-width: 480px) {
    .character-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .character-content {
        padding: 12px;
    }
    
    .character-card {
        padding: 6px;
    }
    
    .character-name {
        font-size: 9px;
    }
    
    .character-title {
        font-size: 7px;
    }
    
    .star {
        font-size: 8px;
    }
    
    .top-nav .page-title {
        font-size: 16px;
    }
    
    .resource-item {
        padding: 4px 8px;
    }
    
    .resource-item span {
        font-size: 12px;
    }
}

/* Mobile Portrait */
@media (max-width: 479px) {
    .character-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .character-content {
        padding: 10px;
    }
    
    .character-card {
        padding: 5px;
        aspect-ratio: 0.8;
    }
    
    .character-name {
        font-size: 8px;
        line-height: 1.1;
    }
    
    .character-title {
        font-size: 6px;
    }
    
    .star {
        font-size: 7px;
    }
    
    .top-nav {
        padding: 8px 10px;
    }
    
    .top-nav .page-title {
        font-size: 14px;
    }
    
    .resource-item {
        padding: 3px 6px;
        margin: 0 2px;
    }
    
    .resource-item span {
        font-size: 10px;
    }
    
    .plus-btn {
        width: 16px;
        height: 16px;
        font-size: 10px;
    }
    
    .sidebar-title {
        font-size: 14px;
    }
    
    .sidebar-menu a {
        font-size: 12px;
        padding: 8px 15px;
    }
}

/* Extra Small Mobile */
@media (max-width: 360px) {
    .character-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .character-card {
        padding: 4px;
    }
    
    .character-name {
        font-size: 7px;
    }
    
    .character-title {
        font-size: 5px;
    }
    
    .star {
        font-size: 6px;
    }
    
    .top-nav .page-title {
        font-size: 12px;
    }
    
    .resource-item span {
        font-size: 9px;
    }
}

/* Character Detail Page Media Queries */
@media (max-width: 768px) {
    .character-main {
        flex-direction: column;
        gap: 15px;
    }
    
    .character-image-panel {
        flex: 0 0 auto;
        max-width: 100%;
        height: auto;
    }
    
    .character-portrait {
        max-height: 40vh;
    }
    
    .character-info-panel {
        flex: 1;
        max-height: 50vh;
    }
    
    .tab-btn {
        font-size: 12px;
        padding: 6px 8px;
    }
    
    .profile-item {
        padding: 6px 8px;
    }
    
    .profile-label,
    .profile-value {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .character-portrait {
        max-height: 35vh;
        padding: 8px;
    }
    
    .character-name-display h2 {
        font-size: 1.1rem;
    }
    
    .character-title-display {
        font-size: 0.8rem;
    }
    
    .tab-btn {
        font-size: 10px;
        padding: 5px 6px;
    }
    
    .character-info-panel {
        padding: 8px;
    }
}
