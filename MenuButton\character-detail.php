<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required files
require_once '../includes/resource_manager.php';
require_once '../includes/character_manager.php';

// Get character ID from URL
$characterId = isset($_GET['id']) ? (int)$_GET['id'] : 1;

// Get character data
$characterManager = new CharacterManager();
$character = $characterManager->getCharacterById($characterId);

// Redirect if character not found
if (!$character) {
    header("Location: Character.php");
    exit();
}

// Get user resources
$user_id = $_SESSION['user_id'];
$resources = ResourceManager::getUserResources($user_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - <?php echo htmlspecialchars($character['name']); ?> Profile</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="character-detail.css">
    <link rel="stylesheet" href="mediastyle.css">
</head>
<body style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%); background-attachment: fixed;">
    <div class="character-detail-container">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <a href="Character.php" class="back-btn style-text">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title"><?php echo htmlspecialchars($character['name']); ?></div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="character-main">
            <!-- Left Panel - Character Image -->
            <div class="character-image-panel">
                <div class="character-portrait">
                    <!-- Character Name and Title Above Image -->
                    <div class="character-name-display">
                        <h2><?php echo htmlspecialchars($character['name']); ?></h2>
                        <h3 class="character-title-display"><?php echo htmlspecialchars($character['title']); ?></h3>
                    </div>

                    <img src="<?php echo $character['portrait']; ?>" alt="<?php echo htmlspecialchars($character['name']); ?>"
                         onerror="this.src='<?php echo $character['image']; ?>'">

                    <!-- Rarity Stars -->
                    <div class="rarity-display">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo $i <= $character['rarity'] ? 'filled' : 'empty'; ?>">★</span>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Character Info -->
            <div class="character-info-panel">
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" onclick="showTab('profile')">Profile</button>
                    <button class="tab-btn" onclick="showTab('gallery')">Gallery</button>
                    <button class="tab-btn" onclick="showTab('info')">Info</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Profile Tab -->
                    <div id="profile-tab" class="tab-panel active">
                        <div class="character-profile">
                            <h3>💖 Character Profile</h3>

                            <div class="profile-info">
                                <div class="profile-item">
                                    <span class="profile-label">Name:</span>
                                    <span class="profile-value"><?php echo htmlspecialchars($character['name']); ?></span>
                                </div>

                                <div class="profile-item">
                                    <span class="profile-label">Title:</span>
                                    <span class="profile-value"><?php echo htmlspecialchars($character['title']); ?></span>
                                </div>

                                <div class="profile-item">
                                    <span class="profile-label">Element:</span>
                                    <span class="profile-value">
                                        <?php echo CharacterManager::getElementIcon($character['element']); ?>
                                        <?php echo ucfirst($character['element']); ?>
                                    </span>
                                </div>

                                <div class="profile-item">
                                    <span class="profile-label">Rarity:</span>
                                    <span class="profile-value">
                                        <?php for($i = 1; $i <= $character['rarity']; $i++): ?>
                                            ⭐
                                        <?php endfor; ?>
                                        (<?php echo $character['rarity']; ?> Star)
                                    </span>
                                </div>

                                <div class="profile-item">
                                    <span class="profile-label">Status:</span>
                                    <span class="profile-value <?php echo $character['status']; ?>">
                                        <?php echo $character['status'] === 'unlocked' ? '💖 In Collection' : '🔒 Locked'; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="character-description-profile">
                                <h4>📝 About</h4>
                                <p><?php echo htmlspecialchars($character['description']); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Gallery Tab -->
                    <div id="gallery-tab" class="tab-panel">
                        <div class="gallery-container">
                            <h3>🖼️ Character Gallery</h3>
                            <div class="gallery-grid">
                                <div class="gallery-item">
                                    <img src="<?php echo $character['image']; ?>" alt="<?php echo htmlspecialchars($character['name']); ?> - Main"
                                         onerror="this.src='../assets/icons/cards.png'">
                                    <div class="gallery-caption">Main Portrait</div>
                                </div>

                                <div class="gallery-item">
                                    <img src="<?php echo $character['portrait']; ?>" alt="<?php echo htmlspecialchars($character['name']); ?> - Portrait"
                                         onerror="this.src='<?php echo $character['image']; ?>'">
                                    <div class="gallery-caption">Character Portrait</div>
                                </div>

                                <!-- Placeholder for additional images -->
                                <div class="gallery-item placeholder">
                                    <div class="placeholder-content">
                                        <i class="fas fa-plus"></i>
                                        <span>More images coming soon...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Info Tab -->
                    <div id="info-tab" class="tab-panel">
                        <div class="character-info-section">
                            <div class="character-description">
                                <h3>📜 Description</h3>
                                <p><?php echo htmlspecialchars($character['description']); ?></p>
                            </div>

                            <div class="character-lore">
                                <h3>📚 Background Story</h3>
                                <p><?php echo htmlspecialchars($character['lore']); ?></p>
                            </div>

                            <div class="character-personality">
                                <h3>💭 Personality Traits</h3>
                                <div class="personality-tags">
                                    <?php
                                    // Generate personality traits based on element
                                    $personalities = [
                                        'fire' => ['Passionate', 'Energetic', 'Bold', 'Determined'],
                                        'ice' => ['Calm', 'Elegant', 'Mysterious', 'Graceful'],
                                        'lightning' => ['Quick-witted', 'Dynamic', 'Powerful', 'Confident'],
                                        'water' => ['Gentle', 'Adaptive', 'Caring', 'Intuitive'],
                                        'earth' => ['Stable', 'Reliable', 'Strong', 'Protective'],
                                        'wind' => ['Free-spirited', 'Agile', 'Cheerful', 'Independent'],
                                        'light' => ['Pure', 'Hopeful', 'Kind', 'Inspiring'],
                                        'dark' => ['Mysterious', 'Complex', 'Intense', 'Alluring']
                                    ];

                                    $traits = $personalities[$character['element']] ?? ['Unique', 'Special', 'Charming', 'Lovely'];
                                    foreach($traits as $trait): ?>
                                        <span class="personality-tag"><?php echo $trait; ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Notification Element -->
    <div id="notification" class="notification"></div>

    <!-- Include JavaScript -->
    <script src="shared-functions.js"></script>
    <script src="character-detail.js"></script>

    <script>
        // Pass PHP data to JavaScript
        const characterData = {
            id: <?php echo $characterId; ?>,
            name: '<?php echo addslashes($character['name']); ?>',
            title: '<?php echo addslashes($character['title']); ?>',
            element: '<?php echo $character['element']; ?>',
            rarity: <?php echo $character['rarity']; ?>,
            status: '<?php echo $character['status']; ?>'
        };
    </script>
</body>
</html>
